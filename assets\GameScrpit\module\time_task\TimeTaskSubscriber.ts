import { ApiHand<PERSON> } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { TimeTaskResponse } from "../../game/net/protocol/Activity";
import { TimeTaskModule } from "./TimeTaskModule";
// 第三步
export class TimeTaskSubscriber {
  // 时间任务通知
  private onTimeTaskNotice(resp: TimeTaskResponse) {
    // 更新任务完成进度 当收到推送是更新本地任务数据
    TimeTaskModule.data.updateTask(resp.taskId, resp.targetVal);
  }
  public register() {
    //订阅服务器消息 当收到时间任务通知时 调用onTimeTaskNotice方法
    ApiHandler.instance.subscribe(TimeTaskResponse, ActivityCmd.TimeTaskNotice, this.onTimeTaskNotice);
  }
  public unRegister() {
    //取消订阅服务器消息 退出限时任务时 取消订阅消息
    ApiHandler.instance.unSubscribe(ActivityCmd.TimeTaskNotice, this.onTimeTaskNotice);
  }
}
