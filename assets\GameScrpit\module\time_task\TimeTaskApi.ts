import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiH<PERSON>lerFail, ApiHandlerSuccess, Net_Code } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { ActivityTakeResponse, TimeTaskMessage, TimeTaskTakeRequest } from "../../game/net/protocol/Activity";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { ActivityID } from "../activity/ActivityConstant";
import { TimeTaskModule } from "./TimeTaskModule";
/**
 * 第四步 向服务器获取数据
 */
export class TimeTaskApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   console.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       console.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       console.log(`${errorCode}`);
  //       console.log(data);
  //     }
  //   );
  // }
  // 路由: 14 - 35  --- 【获取限时任务信息】 --- 【ActivityAction:950】【timeTaskInfo】
  //     方法参数: LongValue
  //     方法返回值: TimeTaskMessage
  // timeTaskInfo = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 35),

  /**
   *
   * @param success 发送请求 参数 ActivityID.TIME_TASK_1
   * @param error
   */
  timeTaskInfo(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    //
    ApiHandler.instance.requestSync(
      TimeTaskMessage,
      ActivityCmd.timeTaskInfo,
      LongValue.encode({ value: ActivityID.TIME_TASK_1 }),
      // 数据回调
      (data: TimeTaskMessage) => {
        //
        TimeTaskModule.data.timeTaskMessage = data;
        console.log(data);
      }
    );
  }
  // 路由: 14 - 36  --- 【领取限时任务完成奖励】 --- 【ActivityAction:970】【takeTimeTask】
  //     方法参数: TimeTaskTakeRequest
  //     方法返回值: ActivityTakeResponse
  // takeTimeTask = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 36),
  takeTimeTask(taskId: number, index: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let request: TimeTaskTakeRequest = {
      activityId: ActivityID.TIME_TASK_1,
      taskId: taskId,
      index: index,
      takeAll: false,
    };
    console.log(request);
    ApiHandler.instance.requestSync(
      ActivityTakeResponse,
      ActivityCmd.takeTimeTask,
      TimeTaskTakeRequest.encode(request),
      (data: ActivityTakeResponse) => {
        //
        TimeTaskModule.data.updateTakeList(taskId, data?.takeList);
        success && success(data);
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        //
        error && error(errorCode, msg, data);
        return false;
      }
    );
  }
}
