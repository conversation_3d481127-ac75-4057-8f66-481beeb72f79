import { _decorator, Component, Node } from "cc";
import { TimeTestResponse } from "../../GameScrpit/game/net/protocol/Activity";
import { TimeTestModule } from "./TimeTestModule";
import { ApiHandler } from "../../GameScrpit/game/mgr/ApiHandler";
import { ActivityCmd } from "../../GameScrpit/game/net/cmd/CmdData";
const { ccclass, property } = _decorator;

/**
 *  订阅服务器消息
 *  订阅服务器消息，处理服务器发送的消息
 *  订阅服务器消息，处理服务器发送的消息，更新本地数据
 */
@ccclass("TimeTestSubscriber")
export class TimeTestSubscriber extends Component {
  //订阅服务器消息
  private onTimeTestNotice(resp: TimeTestResponse) {
    console.log("收到任务更新通知:", resp);
    TimeTestModule.data.updateTask(resp.taskId, resp.targetVal);
  }

  //订阅消息者
  public register() {
    //TimeTestResponse  需要在 Activity 中进行实现 否则会导致类型错误
    ApiHandler.instance.subscribe(TimeTestResponse, ActivityCmd.timeTestInfo, this.onTimeTestNotice);
  }

  //取消订阅
  public unRegister() {
    console.log("取消 TimeTest 消息订阅");
    ApiHandler.instance.unSubscribe(ActivityCmd.timeTestInfo, this.onTimeTestNotice);
  }
}
