import { _decorator, Component, Node } from "cc";
import { TimeTestResponse } from "../../GameScrpit/game/net/protocol/Activity";
import { TimeTestModule } from "./TimeTestModule";
import { ApiHandler } from "../../GameScrpit/game/mgr/ApiHandler";
import { ActivityCmd } from "../../GameScrpit/game/net/cmd/CmdData";
const { ccclass, property } = _decorator;

/**
 *  订阅服务器消息
 *  订阅服务器消息，处理服务器发送的消息
 *  订阅服务器消息，处理服务器发送的消息，更新本地数据
 */
@ccclass("TimeTestSubscriber")
export class TimeTestSubscriber extends Component {
  //订阅服务器消息
  private onTimeTestNotice(resp: TimeTestResponse) {
    console.log("收到任务更新通知:", resp);
    TimeTestModule.data.updateTask(resp.taskId, resp.targetVal);
  }

  //订阅消息者
  public register() {
    console.log("注册 TimeTest 任务更新推送订阅");
    // 订阅服务器主动推送的任务更新通知 (路由 14-65)
    ApiHandler.instance.subscribe(TimeTestResponse, ActivityCmd.TimeTaskNotice, this.onTimeTestNotice);
  }

  //取消订阅
  public unRegister() {
    console.log("取消 TimeTest 任务更新推送订阅");
    ApiHandler.instance.unSubscribe(ActivityCmd.TimeTaskNotice, this.onTimeTestNotice);
  }
}
