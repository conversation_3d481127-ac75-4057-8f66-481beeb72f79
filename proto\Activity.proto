syntax = "proto3";
package sim;
import "Player.proto";
import "Comm.proto";

// 
message AchieveMessage {
  // 成就互动ID
  int64 id = 1;
  // 当前成就值
  int64 targetVal = 2;
  // 是否支付
  bool paid = 3;
  // 已经领取基础奖励的列表
  repeated int32 basicTakeList = 4;
  // 已经领取奖励的列表
  repeated int32 paidTakeList = 5;
}

// 
message AchievePayResponse {
  int64 activityId = 1;
  // 成就ID
  int64 achieveId = 2;
}

// 
message AchieveRewardRequest {
  // 基金的ID
  int64 activityId = 1;
  // 成就ID
  int64 achieveId = 2;
  // 领取的索引处对应的奖励 从0开始
  int32 index = 3;
  // 领取所有可领取的奖励
  bool takeAll = 4;
}

// 
message AchieveRewardResponse {
  int64 activityId = 1;
  // 
  AchieveMessage achieve = 2;
  // 奖励情况
  repeated double rewardList = 3;
}

// 
message AchieveTargetValResponse {
  int64 activityId = 1;
  int64 achieveId = 2;
  int64 targetVal = 3;
}

// 
message ActivityDrawRequest {
  // 活动ID
  int64 activityId = 1;
  // 是否十连抽奖
  bool tenth = 2;
}

// 
message ActivityRechargeResponse {
  // 充值任务完成情况
  LeaderRechargeMessage leaderRecharge = 1;
  repeated double rewardList = 2;
}

// 
message ActivityRechargeUpdateMessage {
  // 充值的活动
  int64 activityId = 1;
  // 充值获得的奖励
  repeated double rewardList = 2;
  // 兑换的道具列表
  int64 redeemId = 3;
}

// 
message ActivityTakeRequest {
  int64 activityId = 1;
  int32 index = 2;
  // 是否一次性领取所有可领取的奖励
  bool takeAll = 3;
}

// 
message ActivityTakeResponse {
  repeated double rewardList = 1;
  repeated int32 takeList = 2;
}

// 
message ActivityUpdateMessage {
  // 需要更新的活动ID配置
  repeated int64 configIdList = 1;
  // 新开启的活动集合
  repeated int64 addList = 2;
  // 生效的活动集合
  repeated int64 closeList = 3;
  // 总的生效的模块ID集合
  repeated int64 totalList = 4;
}

// 
message ActivityUseRequest {
  int64 activityId = 1;
  // 是否十连消耗
  bool isTenUse = 2;
}

// 
message AdventureChooseRequest {
  int64 activityId = 1;
  int64 bigPrizeId = 2;
}

// 
message AdventureDrawResponse {
  // 抽中的道具ID列表
  repeated int64 drawItemList = 1;
  // 奖励列表
  repeated double rewardList = 2;
  // 中大奖的次数
  int32 bigWinCount = 3;
  // 当前轮次已经抽奖的次数   值为0时表示此次有中大奖,drawItemList最后一个就是大奖
  int32 curCostCount = 4;
  // 大奖选中分布
  map<int64,int32> bigChosenMap = 5;
}

// 
message AdventureMessage {
  int64 activityId = 1;
  map<int64,AchieveMessage> achieveMap = 2;
  // 物料ID:兑换次数
  map<int64,int32> redeemMap = 3;
  // 物料ID:广告次数
  map<int64,int32> adMap = 4;
  // 解锁条件：值
  map<int64,int64> limitMap = 5;
  // 自选道具ID：选中的索引列表
  map<int64,sim.CommIntegerListMessage> chosenMap = 6;
  // 本轮选中的大奖的ID 小于等于0表示未选中
  int64 curBigPrizeId = 7;
  // 中大奖的次数
  int32 bigWinCount = 8;
  // 当前轮次已经抽奖的次数
  int32 curCostCount = 9;
  // 大奖选中分布
  map<int64,int32> bigChosenMap = 10;
  // 累计探险次数的奖励领取索引集合
  repeated int32 achieveTakeList = 11;
  // 今日任务列表(为任务列表配置的索引)
  repeated int32 taskIndexList = 12;
  // 今日任务完成数量
  repeated int32 targetValList = 13;
  // 今日任务领取奖励的列表
  repeated int32 taskTakeList = 14;
}

// 
message ConsumeTaskMessage {
  int64 activityId = 1;
  // 物料ID:兑换次数
  map<int64,int32> redeemMap = 2;
  // 物料ID:广告次数
  map<int64,int32> adMap = 3;
  // 限制条件 兑换解锁要求的值
  map<int64,int64> limitMap = 4;
  // 成就
  map<int64,AchieveMessage> achieveMap = 5;
  // 当前选中的道具 小于0，代表没有选中
  int64 chosenItemId = 6;
  // 是否有领取排行榜奖励
  bool boardTake = 7;
}

// 
message ConsumeTaskUseResponse {
  // 本次使用道具的数量
  int32 count = 1;
  // 
  sim.RewardMessage rewardMessage = 2;
}

// 
message DaySignMessage {
  // 今日是否已经签到
  bool sign = 1;
  // 是否已充值
  bool paid = 2;
  // 领取基础签到奖励 0-6
  repeated int32 basicList = 3;
  // 领取付费签到奖励 0-6
  repeated int32 paidList = 4;
}

// 
message DayTaskMessage {
  // 任务完成数量
  repeated int32 targetValList = 1;
  // 已领取奖励的索引列表
  repeated int32 takeList = 2;
}

// 
message DayTaskResponse {
  int64 activityId = 1;
  // 任务完成数量
  repeated int64 targetValList = 2;
}

// 
message FinishMessage {
  // 完成次数
  int32 count = 1;
  // 是否有领取奖励
  bool take = 2;
}

// 
message FirstRechargeMessage {
  // 当前已购买首充档位
  map<int64,FirstSubRechargeMessage> subRechargeMap = 1;
}

// 
message FirstRechargeSignRequest {
  // 首充分档位ID
  int64 id = 1;
  // 第几日的奖励 从0开始
  int32 index = 2;
}

// 
message FirstRechargeSignResponse {
  // 
  FirstSubRechargeMessage message = 1;
  repeated double rewardList = 2;
}

// 
message FirstSubRechargeMessage {
  // 档位ID
  int64 id = 1;
  // 长度为每一档领取的奖励的配置长度  未解锁 -1;可领取 0;已领取 1
  repeated int32 stateList = 2;
}

// 
message FractureAnswerRequest {
  int64 activityId = 1;
  int32 answer = 2;
}

// 
message FractureDrawResponse {
  // 当前触发的事件ID
  int64 fractureId = 1;
  // 当触发的是事件类型，这里表示的是对应关联ID
  int64 choiceId = 2;
  // 剩余的可探索数量
  map<int32,int32> remainFloorCountMap = 3;
  // 奖励
  sim.RewardMessage rewardMessage = 4;
}

// 
message FractureEventResponse {
  // 当前事件ID
  int64 fractureId = 1;
  // 当触发的是事件类型，这里表示的是对应关联ID
  int64 choiceId = 2;
  // 剩余的可探索数量
  map<int32,int32> remainFloorCountMap = 3;
  // 
  sim.RewardMessage rewardMessage = 4;
}

// 
message FractureFightMessage {
  // monsterId 和  npcMessage只生效一个，先判断npcMessage是否为空，非空对战的是玩家，否则为配置表怪物
  int64 monsterId = 1;
  // 当前已挑战过的次数
  int32 fightCount = 2;
  // npc类型，当前触发的是真实玩家
  sim.PlayerSimpleMessage npcMessage = 3;
  // 触发的是小怪/BOSS/玩家剩余的血量
  double remainHp = 4;
  // 冷却时间，如果战斗有冷却时间，必须在这个时间后发起
  int64 fightColdTime = 5;
}

// 
message FractureFightResponse {
  // 当前触发的事件ID
  int64 fractureId = 1;
  // 当触发的是事件类型，这里表示的是对应关联ID
  int64 choiceId = 2;
  // 剩余的可探索数量
  map<int32,int32> remainFloorCountMap = 3;
  // 是否胜利
  bool win = 4;
  // Boss关卡战斗回放
  string replay = 5;
  // 
  FractureFightMessage fightMessage = 6;
  // 新增的日志,可能为空
  FractureLogMessage newLog = 7;
  // 奖励
  sim.RewardMessage rewardMessage = 8;
}

// 
message FractureLogAssistResponse {
  int64 activityId = 1;
  // 
  FractureLogMessage logMessage = 2;
  // 剩余的可探索数量
  map<int32,int32> remainFloorCountMap = 3;
  // 参与协助的玩家信息
  sim.PlayerSimpleMessage assistUserMessage = 4;
}

// 
message FractureLogFightResponse {
  // 如果已经别击杀了，则error为true,尤其是已发起协助的情况下，可能被其他玩家已经击杀
  bool error = 1;
  // 如果error为true时有值，更新日志
  FractureLogMessage fractureLogMessage = 2;
  bool win = 3;
  string replay = 4;
  double remainHp = 5;
  // 剩余的可探索数量
  map<int32,int32> remainFloorCountMap = 6;
  repeated sim.PlayerBaseMessage assistMembers = 7;
}

// 
message FractureLogMessage {
  int64 id = 1;
  // 是否发起协助了
  bool assist = 2;
  // 怪物ID
  int64 monsterId = 3;
  // 事件ID
  int64 fractureId = 4;
  // npc信息 如果npc有值，本次战斗对象就是npc玩家，否则取怪物ID
  sim.PlayerSimpleMessage npcMessage = 5;
  // 剩余血量
  double remainHp = 6;
  // 是否已领取奖励
  bool take = 7;
  // 冷却时间
  int64 coldTime = 8;
  // 参与协助的用户的信息
  repeated sim.PlayerBaseMessage helpMembers = 9;
  // 当前已挑战过的次数
  int32 fightCount = 10;
}

// 
message FractureLogRequest {
  int64 activityId = 1;
  int64 logId = 2;
}

// 
message FractureMessage {
  int64 activityId = 1;
  // 物料ID:兑换次数
  map<int64,int32> redeemMap = 2;
  // 物料ID:广告次数
  map<int64,int32> adMap = 3;
  // 限制条件 兑换解锁要求的值
  map<int64,int64> limitMap = 4;
  // 自选礼包的选中情况
  map<int64,sim.CommIntegerListMessage> chosenMap = 5;
  // 当前所处的楼层
  int32 floorId = 6;
  // 当前触发的事件ID 小于等于0表示还未搜索当前楼层
  int64 fractureId = 7;
  // 当触发的是事件类型，这里表示的是对应关联ID
  int64 choiceId = 8;
  // 路障相关
  FractureRoadMessage roadMessage = 9;
  // 小怪，BOSS,NPC类型相关
  FractureFightMessage fightMessage = 10;
  // 剩余的可探索数量
  map<int32,int32> remainFloorCountMap = 11;
  // 是否领取排行榜奖励
  bool boardTake = 12;
}

// 
message FractureRoadAssistResponse {
  int64 activityId = 1;
  int32 assistCount = 2;
  // 参与协助的玩家信息
  sim.PlayerSimpleMessage assistUserMessage = 3;
}

// 
message FractureRoadMessage {
  // 是否发起了协助
  bool assist = 1;
  // 已经被协助的数量
  int32 assistCount = 2;
  // 路障的开放时间
  int64 roadDeadline = 3;
}

// 
message FractureSearchResponse {
  // 当前触发的事件ID
  int64 fractureId = 1;
  // 当触发的是事件类型，这里表示的是对应关联ID
  int64 choiceId = 2;
  // 剩余的可探索数量
  map<int32,int32> remainFloorCountMap = 3;
  // 路障相关
  FractureRoadMessage roadMessage = 4;
  // 小怪，BOSS,NPC类型相关
  FractureFightMessage fightMessage = 5;
  // 搜索获得奖励
  sim.RewardMessage rewardMessage = 6;
}

// 
message FractureSkipResponse {
  // 当前触发的事件ID
  int64 fractureId = 1;
  // 当触发的是事件类型，这里表示的是对应关联ID
  int64 choiceId = 2;
  // 新增的日志(不为空就加入到日志列表中)
  FractureLogMessage newLog = 3;
}

// 
message FractureTrapResponse {
  // 当前事件ID
  int64 fractureId = 1;
  // 当触发的是事件类型，这里表示的是对应关联ID
  int64 choiceId = 2;
  // 剩余的可探索数量
  map<int32,int32> remainFloorCountMap = 3;
  // 
  FractureRoadMessage roadMessage = 4;
  // 完成后的奖励
  sim.RewardMessage rewardMessage = 5;
}

// 
message FractureTravelRequest {
  int64 activityId = 1;
  int32 floorId = 2;
}

// 
message FundMessage {
  int64 activityId = 1;
  map<int64,AchieveMessage> achieveMap = 2;
}

// 
message LeaderFundMessage {
  int64 activityId = 1;
  // 物料ID:兑换次数
  map<int64,int32> redeemMap = 2;
  // 物料ID:广告次数
  map<int64,int32> adMap = 3;
  // 解锁条件：值
  map<int64,int64> limitMap = 4;
  // 自选礼包的选中情况
  map<int64,sim.CommIntegerListMessage> chosenMap = 5;
  // 成就列表 key:成就ID
  map<int64,AchieveMessage> achieveMap = 6;
  // 签到
  DaySignMessage daySign = 7;
  // 任务完成情况
  DayTaskMessage dayTask = 8;
  // 充值进度
  LeaderRechargeMessage leaderRecharge = 9;
}

// 
message LeaderFundRechargeResponse {
  int64 activityId = 1;
  int32 numerator = 2;
}

// 
message LeaderRechargeMessage {
  // 分子
  int32 numerator = 1;
  // 分母
  int32 denominator = 2;
  // 是否领取过奖励
  bool take = 3;
}

// 
message LeaderSignRequest {
  int64 activityId = 1;
  // 第几天，从0开始
  int32 index = 2;
}

// 
message LeaderSignResponse {
  bool sign = 1;
  // 领取基础签到奖励 0-6
  repeated int32 basicList = 2;
  // 领取付费签到奖励 0-6
  repeated int32 paidList = 3;
  repeated double rewardList = 4;
}

// 
message LifeCardBuyResponse {
  // 
  VipCardMessage vipCardMessage = 1;
  repeated double rewardList = 2;
}

// 
message MonthCardBuyResponse {
  // 
  VipCardMessage vipCardMessage = 1;
  repeated double rewardList = 2;
}

// 
message PrayerDrawResponse {
  // 抽中的道具ID列表
  repeated int64 drawItemList = 1;
  // 奖励列表
  repeated double rewardList = 2;
}

// 
message PrayerMessage {
  int64 activityId = 1;
  map<int64,AchieveMessage> achieveMap = 2;
  // 物料ID:兑换次数
  map<int64,int32> redeemMap = 3;
  // 物料ID:广告次数
  map<int64,int32> adMap = 4;
  // 解锁条件：值
  map<int64,int64> limitMap = 5;
  // 自选道具ID：选中的索引列表
  map<int64,sim.CommIntegerListMessage> chosenMap = 6;
  // 今日任务列表(为任务列表配置的索引)
  repeated int32 taskIndexList = 7;
  // 今日任务完成数量
  repeated int32 targetValList = 8;
  // 今日任务领取奖励的列表
  repeated int32 taskTakeList = 9;
}

// 
message ProsperityMessage {
  int64 activityId = 1;
  // 物料ID:兑换次数
  map<int64,int32> redeemMap = 2;
  // 物料ID:广告次数
  map<int64,int32> adMap = 3;
  // 限制条件 兑换活动ID解锁要求的值
  map<int64,int64> limitMap = 4;
  // 自选礼包的选中情况
  map<int64,sim.CommIntegerListMessage> chosenMap = 5;
  // 成就列表 key:成就ID
  map<int64,AchieveMessage> achieveMap = 6;
}

// 
message RechargeRewardTakeResponse {
  // 奖励领取的索引集合
  repeated int32 takeList = 1;
  repeated double rewardList = 2;
}

// 
message RedeemBuyMessage {
  int64 activityId = 1;
  map<int64,int32> redeemMap = 2;
  // 自选礼包的选中情况
  map<int64,sim.CommIntegerListMessage> chosenMap = 3;
  // 获得的道具
  repeated double rewardList = 4;
  // 购买的弹窗礼包ID
  int64 redeemId = 5;
  // 购买数量
  int32 count = 6;
}

// 
message RedeemChosenRequest {
  // 活动ID
  int64 activityId = 1;
  // 商品ID
  int64 redeemId = 2;
  // 自选的道具所在的索引顺序,如果能选多个，传对应多个选中的索引顺序
  repeated int32 chosenIndexList = 3;
  // 购买数量
  int32 count = 4;
}

// 
message RedeemLimitUpdateMessage {
  int64 activityId = 1;
  // 限制条件
  map<int64,int64> limitMap = 2;
}

// 
message RedeemMessage {
  int64 activityId = 1;
  // 物料ID:兑换次数
  map<int64,int32> redeemMap = 2;
  // 物料ID:广告次数
  map<int64,int32> adMap = 3;
  // 限制条件 兑换解锁要求的值
  map<int64,int64> limitMap = 4;
  // 自选礼包的选中情况
  map<int64,sim.CommIntegerListMessage> chosenMap = 5;
  // 重置时间
  int64 resetTime = 6;
}

// 
message RedeemRequest {
  // 活动ID
  int64 activityId = 1;
  // 商品ID
  int64 redeemId = 2;
  // 如果付费购买或免费兑换道具，表示购买的数量;否则，默认传1
  int32 count = 3;
}

// 
message RedeemResponse {
  // 购买情况 key:商品ID
  map<int64,int32> redeemMap = 1;
  // 物料ID:广告次数
  map<int64,int32> adMap = 2;
  // 自选礼包的选中情况
  map<int64,sim.CommIntegerListMessage> chosenMap = 3;
  // 奖励
  repeated double rewardList = 4;
}

// 
message SevenDaySignMessage {
  // 活动ID
  int64 activityId = 1;
  // 已经签到的天数
  int32 count = 2;
  // 是否今日已签到
  bool sign = 3;
  // 活动的结束时间
  int64 endTime = 4;
}

// 
message SevenDaySignResponse {
  // 
  SevenDaySignMessage sevenDaySign = 1;
  // 
  sim.RewardMessage rewardMessage = 2;
}

// 
message SevenDaySignRewardRequest {
  // 七日活动
  int64 activityId = 1;
  // 签到第几天
  int32 index = 2;
}

// 
message SimplePointMessage {
  // 用户信息
  sim.PlayerDetailMessage detailMessage = 1;
  double pre = 2;
  double after = 3;
  // 积分
  double point = 4;
}

// 
message SimpleRankMessage {
  double pre = 1;
  double after = 2;
  double point = 3;
  int32 rank = 4;
  repeated SimplePointMessage rankList = 5;
}

// 
message TaskCompleteMessage {
  int64 targetVal = 1;
  repeated int32 takeList = 2;
}

message TimeTestMessage {
  int64 activityId = 1;
  map<int64,TaskCompleteMessage> completeMap = 2;
}

// 
message TempleLikeResponse {
  // 今日已经点赞的子殿ID集合
  repeated int64 likeList = 1;
  // 已经获得的神迹效果ID：剩余的生效次数
  map<int64,int32> miracleRemainMap = 2;
  // 奖励
  sim.RewardMessage rewardMessage = 3;
  // 新生效的神迹ID 小于等于0就是没有获得新的神迹
  int64 newMiracleId = 4;
}

// 
message TempleMessage {
  // 子殿ID，获得子殿称号的玩家信息
  map<int64,sim.PlayerTempleMessage> placeMap = 1;
  // 今日已经点赞的子殿ID集合
  repeated int64 likeList = 2;
  // 已经获得的神迹效果ID：剩余的生效次数
  map<int64,int32> miracleRemainMap = 3;
  // 逆袭之路的总值
  int32 totalTreeVal = 4;
  // 逆袭之路领取的奖励索引集合
  repeated int32 treeTakeList = 5;
}

// 
message TimeTaskMessage {
  int64 activityId = 1;
  map<int64,TaskCompleteMessage> completeMap = 2;
}

// 
message TimeTaskResponse {
  int64 activityId = 1;
  int64 taskId = 2;
  int64 targetVal = 3;
}

// 
message TimeTaskTakeRequest {
  int64 activityId = 1;
  int64 taskId = 2;
  int32 index = 3;
  // 是否一次性领取所有可领取的奖励
  bool takeAll = 4;
}

/** 限时测试任务领取响应 */
message TimeTestResponse{
 int64 activityId = 1;
  int64 taskId = 2;
  int64 targetVal = 3;
}


/** 限时测试任务领取请求 */
message TimeTestRequest {
   int64 activityId = 1;
  int64 taskId = 2;
  int32 index = 3;
  // 是否一次性领取所有可领取的奖励
  bool takeAll = 4;
}

// 
message TopUpMessage {
  int64 activityId = 1;
  // 物料ID:兑换次数
  map<int64,int32> redeemMap = 2;
  // 物料ID:广告次数
  map<int64,int32> adMap = 3;
  // 限制条件 兑换活动ID解锁要求的值
  map<int64,int64> limitMap = 4;
  // 自选礼包的选中情况
  map<int64,sim.CommIntegerListMessage> chosenMap = 5;
  // 今日领取的充值ID 对应配置表的ID
  int64 signId = 6;
  // 一轮循环的签到状态
  map<int64,int32> signMap = 7;
  // 总消费金额
  int64 totalRecharge = 8;
  // 领取的累计金额索引列表，索引从0开始
  repeated int32 takeList = 9;
  // 截止时间
  int64 deadline = 10;
}

// 
message TopUpRewardRequest {
  int64 activityId = 1;
  // 累计充值赠送道具索引
  int32 index = 2;
}

// 
message TopUpRewardResponse {
  // 领取的累计金额索引列表，索引从0开始
  repeated int32 takeList = 1;
  repeated double rewardList = 2;
}

// 
message TopUpSignRequest {
  int64 activityId = 1;
  int64 signId = 2;
}

// 
message TopUpSignResponse {
  // 一轮循环的签到状态
  map<int64,int32> signMap = 1;
  // 截止时间
  int64 deadline = 2;
  repeated double rewardList = 3;
}

// 
message VipCardMessage {
  // 是否有购买终身卡
  bool life = 1;
  // 月卡的截止有效时间
  int64 deadline = 2;
  // 是否有领取月卡的日常道具奖励
  bool takeMonthDailyReward = 3;
  // 是否有领取终身卡卡的日常道具奖励
  bool takeLifeDailyReward = 4;
}

// 
message VipCardResponse {
  // 
  VipCardMessage vipCard = 1;
  repeated double rewardList = 2;
}

// 
message WindowPackMessage {
  // 活动ID
  int64 activityId = 1;
  // 持续时间 key:弹窗类型 val:截止时间戳
  map<int32,int64> durationMap = 2;
  // 购买情况 商品ID集合
  repeated int64 redeemList = 3;
  // 冷却截止时间   key:弹窗类型 val:冷却截止时间戳
  map<int32,int64> coldMap = 4;
}

// 
message WindowRecordRequest {
  // 活动ID
  int64 activityId = 1;
  // 弹窗类型
  int32 type = 2;
}

// 
message WindowsPackBuyResponse {
  // 道具弹窗
  WindowPackMessage windowPackMessage = 1;
  // 获得的道具
  repeated double rewardList = 2;
  // 购买的弹窗礼包ID
  int64 redeemId = 3;
}

