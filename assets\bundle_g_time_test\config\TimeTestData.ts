import { _decorator, Component, Node } from "cc";
import { TimeTestMessage } from "../../GameScrpit/game/net/protocol/Activity";
const { ccclass, property } = _decorator;
/**
 *  限时测试任务数据
 */
@ccclass("TimeTestData")
export class TimeTestData {
  // 获取任务数据
  private _timeTestMessage: TimeTestMessage;

  // 获取任务数据
  get timeTestMessage() {
    return this._timeTestMessage;
  }

  // 更新任务数据
  set timeTestMessage(value: TimeTestMessage) {
    this._timeTestMessage = value;
  }
  /**
   * 更新任务领取列表
   * @param taskId 任务id
   * @param takeList 领取列表
   * @returns
   */

  updateTakeList(taskId: number, takeList: number[]) {
    console.log("更新领取列表:", { taskId, takeList });

    if (!this._timeTestMessage) {
      console.warn("timeTestMessage 不存在，无法更新 takeList");
      return false;
    }
    if (!this._timeTestMessage.completeMap) {
      console.warn("completeMap 不存在，无法更新 takeList");
      return false;
    }
    if (!this._timeTestMessage.completeMap[taskId]) {
      console.warn(`taskId ${taskId} 在 completeMap 中不存在，无法更新 takeList`);
      console.log("当前 completeMap 的 keys:", Object.keys(this._timeTestMessage.completeMap));
      return false;
    }

    const oldTakeList = [...(this._timeTestMessage.completeMap[taskId].takeList || [])];
    this._timeTestMessage.completeMap[taskId].takeList = takeList || [];

    console.log("takeList 更新完成:", {
      taskId,
      oldTakeList,
      newTakeList: this._timeTestMessage.completeMap[taskId].takeList,
      changed: JSON.stringify(oldTakeList) !== JSON.stringify(this._timeTestMessage.completeMap[taskId].takeList),
    });

    return true;
  }

  /**
   * 更新任务目标值
   * @param taskId 任务id
   * @param targetVal 目标值
   * @returns
   */
  updateTask(taskId: number, targetVal: number) {
    console.log("更新任务目标值:", { taskId, targetVal });

    if (!this._timeTestMessage) {
      console.warn("timeTestMessage 不存在，无法更新任务");
      return false;
    }
    if (!this._timeTestMessage.completeMap) {
      console.warn("completeMap 不存在，无法更新任务");
      return false;
    }
    if (!this._timeTestMessage.completeMap[taskId]) {
      console.warn(`taskId ${taskId} 在 completeMap 中不存在，无法更新任务`);
      console.log("当前 completeMap 的 keys:", Object.keys(this._timeTestMessage.completeMap));
      return false;
    }

    const oldTargetVal = this._timeTestMessage.completeMap[taskId].targetVal;
    this._timeTestMessage.completeMap[taskId].targetVal = targetVal;

    console.log("任务目标值更新完成:", {
      taskId,
      oldTargetVal,
      newTargetVal: targetVal,
      changed: oldTargetVal !== targetVal,
    });

    return true;
  }
}
