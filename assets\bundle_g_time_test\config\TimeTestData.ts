import { _decorator, Component, Node } from "cc";
import { TimeTestMessage } from "../../GameScrpit/game/net/protocol/Activity";
const { ccclass, property } = _decorator;
/**
 *  限时测试任务数据
 */
@ccclass("TimeTestData")
export class TimeTestData {
  // 获取任务数据
  private _timeTestMessage: TimeTestMessage;

  // 获取任务数据
  get timeTestMessage() {
    return this._timeTestMessage;
  }

  // 更新任务数据
  set timeTestMessage(value: TimeTestMessage) {
    this._timeTestMessage = value;
  }
  /**
   * 更新任务领取列表
   * @param taskId 任务id
   * @param takeList 领取列表
   * @returns
   */

  updateTakeList(taskId: number, takeList: number[]) {
    if (!this._timeTestMessage) {
      return;
    }
    if (!this._timeTestMessage.completeMap) {
      return;
    }
    if (!this._timeTestMessage.completeMap[taskId]) {
      return;
    }

    this._timeTestMessage.completeMap[taskId].takeList = takeList;
  }

  /**
   * 更新任务目标值
   * @param taskId 任务id
   * @param targetVal 目标值
   * @returns
   */
  updateTask(taskId: number, targetVal: number) {
    if (!this._timeTestMessage) {
      return;
    }
    if (!this._timeTestMessage.completeMap) {
      return;
    }
    if (!this._timeTestMessage.completeMap[taskId]) {
      return;
    }
    this._timeTestMessage.completeMap[taskId].targetVal = targetVal;
  }
}
