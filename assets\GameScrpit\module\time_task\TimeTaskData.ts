import { JsonMgr } from "../../game/mgr/JsonMgr";
import { TimeTaskMessage } from "../../game/net/protocol/Activity";
import { times } from "../../lib/utils/NumbersUtils";
import { TimeTaskModule } from "./TimeTaskModule";

/**
 * 第五步 数据管理
 *
 */
export class TimeTaskData {
  // 通过set get 访问数据
  private _timeTaskMessage: TimeTaskMessage;
  set timeTaskMessage(value: TimeTaskMessage) {
    this._timeTaskMessage = value;
  }
  get timeTaskMessage() {
    return this._timeTaskMessage;
  }

  /**
   * 更新已领取奖励列表
   * @param taskId 任务id
   * @param takeList 领取列表
   * @returns
   */
  updateTakeList(taskId: number, takeList: number[]) {
    if (!this._timeTaskMessage) {
      return;
    }
    if (!this._timeTaskMessage.completeMap) {
      return;
    }
    if (!this._timeTaskMessage.completeMap[taskId]) {
      return;
    }

    this._timeTaskMessage.completeMap[taskId].takeList = takeList;
  }

  /**
   * 更新任务完成进度
   * @param taskId 任务id
   * @param targetVal 目标值
   * @returns
   */
  updateTask(taskId: number, targetVal: number) {
    if (!this._timeTaskMessage) {
      return;
    }
    if (!this._timeTaskMessage.completeMap) {
      return;
    }
    if (!this._timeTaskMessage.completeMap[taskId]) {
      return;
    }

    this._timeTaskMessage.completeMap[taskId].targetVal = targetVal;
  }
}
