import { _decorator, Component, Node } from "cc";
import { GameData } from "../../GameScrpit/game/GameData";
import { TimeTestConfig } from "./TimeTestConfig";
import { TimeTestData } from "./TimeTestData";
import { TimeTestApi } from "./TimeTestApi";
import { TimeTestService } from "./TimeTestService";
import { TimeTestSubscriber } from "./TimeTestSubscriber";
import { TimeTestRoute } from "./TimeTestRoute";
import { TimeTestViewModel } from "./TimeTestViewModel";

const { ccclass, property } = _decorator;

@ccclass("TimeTestModule")
export class TimeTestModule extends Component {
  // 私有构造函数，防止外部实例化
  private constructor() {
    super();
  }

  // 单例模式
  public static get instance(): TimeTestModule {
    if (!GameData.instance.TimeTestModule) {
      GameData.instance.TimeTestModule = new TimeTestModule();
    }
    return GameData.instance.TimeTestModule;
  }

  //初始化数据

  private _data = new TimeTestData();
  private _api = new TimeTestApi();
  private _service = new TimeTestService();
  private _subscriber = new TimeTestSubscriber();
  private _route = new TimeTestRoute();
  private _viewModel = new TimeTestViewModel();
  private _config = new TimeTestConfig();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get service() {
    return this.instance._service;
  }
  public static get subscriber() {
    return this.instance._subscriber;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }
  public static get config() {
    return this.instance._config;
  }

  public init(data?: any, completedCallback?: Function) {
    // 如果已经与订阅者存在 先注册
    if (this._subscriber) {
      this._subscriber.unRegister();
    }

    //初始化核心组件
    this._data = new TimeTestData(); //数据层
    this._api = new TimeTestApi(); // api接口层
    this._service = new TimeTestService(); // 业务服务层
    this._subscriber = new TimeTestSubscriber(); // 事件订阅层
    this._route = new TimeTestRoute(); // 路由层
    this._viewModel = new TimeTestViewModel(); //视图模块层
    this._config = new TimeTestConfig(); // 配置管理
    // 注册订阅者
    this._subscriber.register();
    //初始化路由
    this._route.init();
    //获取初始数据
    TimeTestModule.api.timeTestInfo(() => {
      //打印初始数据
      console.log("TimeTest 获取数据:", TimeTestModule.data.timeTestMessage);

      // 有回调函数时 正常执行回调 没有时 什么都不做
      completedCallback && completedCallback();
    });
  }
}
